// app/(components)/(content-layout)/user-management/components/UserManagementPageClient.tsx - Client-side component for user management
"use client";

import { useUserManagement } from "@/shared/hooks";
import { UserListFilters, UserListResponse } from "@/shared/types/user-management-types";
import { useRouter } from 'next/navigation';
import { Fragment, useCallback, useMemo } from "react";
// Performance monitoring simplified - can be re-enabled when needed
// import { useRenderTracker, useLoadTimeTracker, usePaginationTracker, useProfiler } from "@/shared/utils/performanceMonitoring";
import fadeInStyles from '@/app/css/animations/fade-in.module.css';

// Import new global components
import {
	GlobalDataTable,
	GlobalFilterSection,
	GlobalPageHeader,
	FILTER_THEMES
} from "@/shared/UI/components";
import { DEFAULT_USER_VISIBLE_FILTERS, USER_MANAGEMENT_FILTERS } from "@/shared/config/userManagementFilters";

// Direct imports to avoid circular dependency issues
import { SpkErrorMessage } from "@/shared/UI/components";
import UserManagementSkeleton from "./UserManagementSkeleton";

// Import the user table columns logic
import { useModalNavigation } from "@/shared/hooks/ui/useModalNavigation";
import { getUserTableColumns } from "./UserTableColumns";

interface UserManagementPageClientProps {
	initialUserListResponse?: UserListResponse | null;
	initialFilters?: UserListFilters;
}

/**
 * Client-side component that handles all interactive functionality for user management
 * Separated from the server component to maintain SSR SEO benefits
 * Now uses custom hook for business logic separation and global components
 *
 * Features:
 * - Accepts server-side rendered initial data for performance
 * - Graceful fallback to client-side fetching
 * - Optimized re-rendering for pagination changes
 */
export function UserManagementPageClient({
	initialUserListResponse = null,
	initialFilters
}: UserManagementPageClientProps = {}) {
	const router = useRouter();

	// Performance monitoring - simplified for now, can be re-enabled when needed
	// useRenderTracker('UserManagementPageClient', {
	//   hasInitialData: !!initialUserListResponse,
	//   initialFiltersSize: initialFilters?.size
	// });
	// useLoadTimeTracker('UserManagementPage');
	// useProfiler('UserManagementPageClient');
	// const { startPaginationTimer, endPaginationTimer } = usePaginationTracker();

	// Use custom hook for all business logic with initial data
	const {
		filters,
		userListResponse,
		isLoading,
		isError,
		error,
		isFetching,
		totalUsers: _totalUsers,
		activeUsers: _activeUsers,
		inactiveUsers: _inactiveUsers,
		handleFilterChange,
		handlePageChange,
		handleRefresh,
		isAuthenticated,
		hasHydrated
	} = useUserManagement({
		initialUserListResponse,
		initialFilters
	});
	// console.log(userListResponse,'userListResponse');
	// Get modal navigation functions
	const { openEditUserModal, openDeactivateUserModal, openActivateUserModal, openCreateUserModal } = useModalNavigation();

	// Memoize table columns for performance
	const columns = useMemo(() => getUserTableColumns({
		openEditUserModal,
		openDeactivateUserModal,
		openActivateUserModal
	}), [openEditUserModal, openDeactivateUserModal, openActivateUserModal]);

	const _handleAddUser = useCallback(() => {
		router.push('/user-management/create');
	}, [router]);

	// Handle items per page change - optimized with useCallback
	const handleItemsPerPageChange = useCallback((itemsPerPage: number) => {
		handleFilterChange({ size: itemsPerPage });
	}, [handleFilterChange]);

	// Enhanced pagination handler - performance tracking can be re-enabled when needed
	const handlePageChangeWithTracking = useCallback((page: number) => {
		// startPaginationTimer(); // Performance tracking disabled for now
		handlePageChange(page);
		// End timer will be called when data loads (in useEffect)
	}, [handlePageChange]);

	// Track pagination completion - disabled for now
	// React.useEffect(() => {
	//   if (!isLoading && !isFetching) {
	//     endPaginationTimer(filters.page);
	//   }
	// }, [isLoading, isFetching, filters.page, endPaginationTimer]);

	// Don't render anything if not authenticated and hydrated
	if (hasHydrated && !isAuthenticated) {
		return null;
	}

	// Show loading state with skeleton
	if (isLoading) {
		return <UserManagementSkeleton />;
	}

	return (
		<Fragment>
			{/* Global Page Header */}
			<GlobalPageHeader
				title="User Management"
				bgIcon="ri-team-line"
				actions={[
					{
						label: "Add New User",
						icon: "ri-user-add-line",
						onClick: openCreateUserModal,
					}
				]}
			/>

			{/* Main Content with Enhanced Design */}
			<div className={`grid grid-cols-12 gap-6 ${fadeInStyles.fadeIn}`}>
				{/* Global Filter Section */}
				<div className="xl:col-span-12 col-span-12">
					<GlobalFilterSection
						filters={filters}
						onFilterChange={handleFilterChange}
						isLoading={isLoading || isFetching}
						onExport={() => { /* Export functionality */ }}
						showExportButton={true}
						availableFilters={USER_MANAGEMENT_FILTERS}
						defaultVisibleFilters={DEFAULT_USER_VISIBLE_FILTERS}
						title="User Filters"
						description="Filter and search users with advanced criteria"
						theme={FILTER_THEMES.USER_MANAGEMENT}
					/>
				</div>

				{/* Enhanced Users Table Section */}
				<div className="xl:col-span-12 col-span-12 transform transition-all duration-500 ease-in-out delay-100 rounded-[16px] overflow-visible relative">
					<div className="bg-filter p-[1rem] rounded-md">
						{isError ? (
							<SpkErrorMessage
								message={error?.message || "Failed to load users"}
								onRetry={handleRefresh}
								variant="alert"
								size="md"
							/>
						) : (
							<GlobalDataTable
								columns={columns}
								data={userListResponse?.data || []}
								isLoading={isLoading}
								showPagination={true}
								currentPage={filters.page}
								totalItems={userListResponse?.count || 0}
								itemsPerPage={filters.size}
								totalPages={userListResponse?.total_pages}
								onPageChange={handlePageChangeWithTracking}
								onItemsPerPageChange={handleItemsPerPageChange}
								showItemsPerPageSelector={true}
								emptyText="No users found. Try adjusting your search filters."
								minHeight="400px"
							/>
						)}

					</div>
				</div>
			</div>
		</Fragment>
	);
}
