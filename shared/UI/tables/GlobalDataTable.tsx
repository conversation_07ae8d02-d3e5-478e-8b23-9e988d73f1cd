import { EnhancedPagination, SpkTable, SpkTableColumn } from "@/shared/UI/components";
import React from "react";

export interface GlobalDataTableProps {
  columns: SpkTableColumn[];
  data: any[];
  isLoading?: boolean;
  className?: string;
  emptyText?: string;

  // Pagination props
  showPagination?: boolean;
  currentPage?: number;
  totalItems?: number;
  itemsPerPage?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
  showItemsPerPageSelector?: boolean;

  // Table styling props
  minHeight?: string;
  tableClass?: string;
  headerClass?: string;
  rowClass?: string;

  // Additional features
  showLoadingOverlay?: boolean;
  loadingText?: string;
}

/**
 * Global Data Table Component
 *
 * Reusable table component for admin pages with:
 * - Consistent card heights within rows and appropriate column widths
 * - Status icons with tooltips instead of text where applicable
 * - Click-to-navigate functionality using Next.js Link components (handled in column render functions)
 * - Existing table styling and responsive design
 * - Integrated pagination with consistent styling
 * - Dark theme compatibility with semantic Tailwind classes
 * - Loading states and empty states
 * - Optimized with React.memo to prevent unnecessary re-renders
 */
const GlobalDataTable: React.FC<GlobalDataTableProps> = React.memo(({
  columns,
  data,
  isLoading = false,
  className = "",
  emptyText = "No data found. Try adjusting your search filters.",

  // Pagination props
  showPagination = false,
  currentPage = 1,
  totalItems = 0,
  itemsPerPage = 10,
  totalPages,
  onPageChange,
  onItemsPerPageChange,
  showItemsPerPageSelector = false,

  // Table styling props
  minHeight = "400px",
  tableClass = "",
  headerClass = "",
  rowClass = "",

  // Additional features
  showLoadingOverlay = false,
  loadingText = "Loading..."
}) => {
  // Ensure data is always an array to prevent map errors
  const safeData = Array.isArray(data) ? data : [];

  // Default table classes with NEW DARK THEME support
  const defaultTableClass = `table-fixed w-full min-w-[1100px] bg-elevated ${tableClass}`;
  const defaultHeaderClass = `bg-table-head ${headerClass}`;
  const defaultRowClass = `h-16 border-b border-table-row hover:bg-elevated transition-colors duration-200 ${rowClass}`;

  return (
    <div style={{ minHeight }} className={`w-full ${className}`}>
      <div className="rounded-lg overflow-visible flex flex-col gap-[0.5rem] relative">
        {/* Loading Overlay */}
        {showLoadingOverlay && isLoading && (
          <div className="absolute inset-0 bg-section/80 flex items-center justify-center z-10 rounded-lg">
            <div className="flex items-center gap-3 text-white">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-400"></div>
              <span className="font-medium">{loadingText}</span>
            </div>
          </div>
        )}

        {/* Table Container - Remove overflow-x-auto to prevent table scrolling */}
        <div className="w-full">
          <SpkTable
            columns={columns}
            data={safeData}
            loading={isLoading && !showLoadingOverlay} // Use SpkTable's loading if not using overlay
            hover={true}
            responsive={false} // We handle responsive manually
            emptyText={emptyText}
            className="global-data-table w-full"
            tableClass={defaultTableClass}
            headerClass={defaultHeaderClass}
            rowClass={defaultRowClass}
            size="md" // Consistent table size
          />
        </div>

        {/* Enhanced Pagination */}
        {showPagination && onPageChange && totalItems > 0 && (
          <EnhancedPagination
            currentPage={currentPage}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onPageChange={onPageChange}
            onItemsPerPageChange={onItemsPerPageChange}
            totalPages={totalPages}
            showItemsPerPageSelector={showItemsPerPageSelector}
            className=""
          />
        )}
      </div>
    </div>
  );
});

GlobalDataTable.displayName = 'GlobalDataTable';

export default GlobalDataTable;
