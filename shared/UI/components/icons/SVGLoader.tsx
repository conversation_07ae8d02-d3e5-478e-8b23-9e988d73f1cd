"use client";

import React, { useState, useEffect } from 'react';

// Define available SVG icon names (extend as needed)
export type SVGIconName =
  | 'plus'
  | 'minus'
  | 'edit'
  | 'delete'
  | 'search'
  | 'filter'
  | 'sort'
  | 'arrow-up'
  | 'arrow-down'
  | 'arrow-left'
  | 'arrow-right'
  | 'check'
  | 'close'
  | 'info'
  | 'warning'
  | 'error'
  | 'success'
  | 'deposit'
  | 'withdraw'
  | 'lastdepositewithdraw'
  | 'totalbets'
  | 'balance'
  | 'deposite'
  // Report icons
  | 'betamount'
  | 'totalwin'
  | 'totalggr'
  | 'totalDeposite'
  | 'totalWithdraw'
  | 'totalBetPlaced'
  | 'balanceReport'
  // Bet slip icons
  | 'tick';

export interface SVGLoaderProps {
  name: SVGIconName;
  className?: string;
  size?: string | number;
  color?: string;
  fallback?: React.ReactNode;
  'aria-label'?: string;
  'aria-hidden'?: boolean;
  title?: string;
}

// SVG content cache
const svgCache = new Map<string, string>();

// Define SVG directories to search
const SVG_DIRECTORIES = [
  '/assets/svg_icons/userStaticsCard',
  '/assets/svg_icons/financialSummary',
  '/assets/svg_icons/betReports',
  '/assets/svg_icons/cashierReports',
  '/assets/svg_icons/financialReports',
  '/assets/svg_icons/bet-slip',
  '/assets/icon-fonts/tabler-icons/icons',
  '/assets/icons/ui',
  '/assets/icons/crypto',
  '/assets/icons/social'
];

// Load SVG content from local files
const loadSVGContent = async (name: SVGIconName): Promise<string> => {
  if (svgCache.has(name)) {
    return svgCache.get(name)!;
  }

  // Try each directory until we find the SVG
  for (const directory of SVG_DIRECTORIES) {
    try {
      const response = await fetch(`${directory}/${name}.svg`);

      if (response.ok) {
        const svgContent = await response.text();
        svgCache.set(name, svgContent);
        return svgContent;
      }
    } catch {
      // Continue to next directory
      continue;
    }
  }

  // No SVG found in any directory
  return '';
};

/**
 * Optimized SVG Loader Component
 *
 * Loads SVG icons from local files with caching for better performance.
 * Recommended over external SVG URLs for production use.
 */
const SVGLoader: React.FC<SVGLoaderProps> = ({
  name,
  className = '',
  size,
  color,
  fallback = '?',
  'aria-label': ariaLabel,
  'aria-hidden': ariaHidden,
  title
}) => {
  const [content, setContent] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadContent = async () => {
      setLoading(true);
      setError(null);

      try {
        const svgContent = await loadSVGContent(name);
        setContent(svgContent);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load SVG');
      } finally {
        setLoading(false);
      }
    };

    loadContent();
  }, [name]);

  const commonStyles: React.CSSProperties = {
    ...(size && { width: size, height: size }),
    ...(color && { color }),
  };

  const commonProps = {
    className,
    style: commonStyles,
    'aria-label': ariaLabel,
    'aria-hidden': ariaHidden,
    title,
  };

  if (loading) {
    return (
      <div {...commonProps} className={`inline-block animate-pulse bg-gray-300 rounded ${className}`}>
        <span className="sr-only">Loading icon...</span>
      </div>
    );
  }

  if (error || !content) {
    return (
      <div {...commonProps} className={`inline-flex items-center justify-center ${className}`}>
        {fallback}
      </div>
    );
  }

  return (
    <div
      {...commonProps}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

// Utility functions for cache management
export const getSVGCacheInfo = () => {
  return {
    size: svgCache.size,
    keys: Array.from(svgCache.keys()),
  };
};

export const clearSVGCache = () => {
  svgCache.clear();
};

// Preload multiple SVGs for better performance
export const preloadSVGs = async (iconNames: SVGIconName[]): Promise<void> => {
  const loadPromises = iconNames.map(name =>
    loadSVGContent(name).catch(() => {
      // Failed to preload SVG - silently continue
    })
  );

  await Promise.allSettled(loadPromises);
};

export default SVGLoader;
