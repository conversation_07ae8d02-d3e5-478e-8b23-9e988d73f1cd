'use client';

import React, { Fragment } from 'react';
import { TurboStarsWebSocketProvider } from '@/shared/providers/TurboStarsWebSocketProvider';
import Footer from '@/shared/layouts-components/footer/footer';
import Header from '@/shared/layouts-components/header/header';
import Backtotop from '@/shared/layouts-components/backtotop/backtotop';
import Loader from '@/shared/layouts-components/loader/loader';
import { InlineSportsbook } from '@/shared/components/sportsbook/InlineSportsbook';
import { BetDetailsPopup } from '@/shared/UI/components';
// Debug components removed after fixing re-render issues

interface ContentLayoutUIProps {
  children: React.ReactNode;
  isAuthenticated: boolean;
  hasHydrated: boolean;
  isBetPopupOpen: boolean;
  currentBetDetails: any;
  hideBetPopup: () => void;
  pathname: string;
}

/**
 * Pure UI component for content layout
 * 
 * This component only handles presentation and receives all data via props.
 * All business logic is handled by the useContentLayoutLogic hook.
 */
export const ContentLayoutUI: React.FC<ContentLayoutUIProps> = ({
  children,
  isAuthenticated,
  hasHydrated,
  isBetPopupOpen,
  currentBetDetails,
  hideBetPopup,
  pathname,
}) => {
  // Debug tracking removed after fixing re-render issues
  // const renderStats = useRenderTracker('ContentLayoutUI');

  // Show loader while hydrating or if not authenticated
  if (!hasHydrated || !isAuthenticated) {
    return <Loader />;
  }
  // Check if current route is sportsbook
  const isSportsbookRoute = pathname?.includes('/sportsbook');
  return (
    <Fragment>
      {/* Debug component removed after fixing re-render issues */}
      <TurboStarsWebSocketProvider autoConnect={false}>
        <div className='page'>
          <Header />
          <main className={`main-content app-content ${isSportsbookRoute ? 'm-0 p-0' : 'pt-0'}`}>
            <div className={isSportsbookRoute ? '' : 'container-fluid page-header-breadcrumb'}>
              {children}
            </div>
          </main>
          <Footer />
        </div>
        <Backtotop />
        <InlineSportsbook />

        {/* Bet Details Popup - rendered at top level for maximum visibility */}
        <BetDetailsPopup
          isOpen={isBetPopupOpen}
          onClose={hideBetPopup}
          betDetails={currentBetDetails}
          showBackdrop={false} // Non-modal overlay
          autoCloseDelay={8000} // Auto-close after 8 seconds
        />
      </TurboStarsWebSocketProvider>
    </Fragment>
  );
};
