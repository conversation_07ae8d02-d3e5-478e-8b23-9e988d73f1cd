# Bet Details Popup System

A comprehensive bet details popup system for the TurboStars sportsbook integration that provides real-time bet notifications with user context validation.

## Overview

The Bet Details Popup system replaces the previous simple notification overlay with a detailed, user-specific popup that displays comprehensive bet information when bets are placed via the TurboStars sportsbook WebSocket integration.

## Features

### 🎯 **User Context Validation**
- Only shows popups for the current authenticated user's bets
- Filters out notifications for other users automatically
- Uses dynamic user ID from authentication store

### 🎨 **Enhanced UX**
- Non-modal overlay design that doesn't block sportsbook interaction
- Smooth entrance/exit animations with scale and translate transforms
- Auto-close functionality with visual countdown timer
- Pause-on-hover feature to prevent accidental closure
- Progress bar with color-coded states (blue = active, yellow = paused)

### ♿ **Accessibility**
- Full keyboard support (ESC key to close)
- Proper ARIA attributes and roles
- Screen reader friendly markup
- Focus management for better navigation

### 🌙 **Theme Compatibility**
- Full dark/light theme support using Tailwind CSS
- Consistent with project design system
- Responsive design for different screen sizes

### 📱 **Responsive Design**
- Optimized for desktop and mobile devices
- Adaptive positioning and sizing
- Touch-friendly interaction areas

## Components

### BetDetailsPopup

The main popup component that displays bet details.

```tsx
import { BetDetailsPopup } from '@/shared/UI/components';

<BetDetailsPopup
  isOpen={isBetPopupOpen}
  onClose={hideBetPopup}
  betDetails={currentBetDetails}
  showBackdrop={false}
  autoCloseDelay={8000}
  className="z-[9999]"
/>
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `isOpen` | `boolean` | - | Whether the popup is visible |
| `onClose` | `() => void` | - | Callback when popup is closed |
| `betDetails` | `CashierTurboPlaceBetDetails \| null` | - | Bet information to display |
| `showBackdrop` | `boolean` | `false` | Show backdrop overlay |
| `autoCloseDelay` | `number` | `5000` | Auto-close delay in ms (0 to disable) |
| `className` | `string` | `''` | Additional CSS classes |

## Integration

### Sportsbook Store

The popup state is managed in the sportsbook store with the following new properties:

```typescript
interface SportsbookState {
  // ... existing properties
  
  // Bet popup state
  isBetPopupOpen: boolean;
  currentBetDetails: CashierTurboPlaceBetDetails | null;
  currentUserId: number | null;
  
  // Bet popup actions
  showBetPopup: (betDetails: CashierTurboPlaceBetDetails) => void;
  hideBetPopup: () => void;
  setCurrentUserId: (userId: number | null) => void;
}
```

### WebSocket Integration

The WebSocket provider has been enhanced with user context validation:

```typescript
// Only show popup if notification is for current user
if (currentUserIdFromAuth && currentUserIdFromAuth === notificationUserId) {
  showBetPopup(notification);
}
```

### Usage in InlineSportsbook

The popup is integrated into the sportsbook component as a non-modal overlay:

```tsx
<BetDetailsPopup
  isOpen={isBetPopupOpen}
  onClose={hideBetPopup}
  betDetails={currentBetDetails}
  showBackdrop={false}
  autoCloseDelay={8000}
  className="z-[9999]"
/>
```

## Technical Implementation

### State Management
- Uses Zustand store for global state management
- Integrates with existing sportsbook state
- Maintains user context for filtering

### WebSocket Integration
- Leverages existing TurboStars WebSocket service
- Adds user validation layer
- Maintains backward compatibility

### Animation System
- CSS transforms for smooth animations
- Configurable timing and easing
- Performance-optimized transitions

### Auto-close System
- Countdown timer with visual feedback
- Pause-on-hover functionality
- Configurable delay settings

## Migration from Previous System

The new popup system replaces the `BetNotificationOverlay` component in the sportsbook integration while maintaining the existing WebSocket infrastructure. The old notification system is still available for testing purposes but has been removed from production sportsbook integration.

### Breaking Changes
- `BetNotificationOverlay` removed from `InlineSportsbook`
- New popup state properties added to sportsbook store
- User context validation now required for notifications

### Backward Compatibility
- Existing WebSocket service unchanged
- Test pages still functional
- API interfaces remain the same

## Future Enhancements

Potential improvements for future versions:
- Sound notifications for bet placement
- Customizable popup positioning
- Multiple bet notifications queue
- Enhanced bet details with more information
- Integration with user balance updates
- Analytics tracking for bet events
