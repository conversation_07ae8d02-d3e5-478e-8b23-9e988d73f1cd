# Bet Notification System

A comprehensive bet notification system for the TurboStars sportsbook integration that provides real-time bet notifications with enhanced print functionality and user context validation.

## Overview

The Bet Notification system provides a modern, user-friendly popup that displays bet confirmation information when bets are placed via the TurboStars sportsbook WebSocket integration. This system has replaced the previous BetDetailsPopup with enhanced print functionality and improved user experience.

## Features

### 🎯 **User Context Validation**
- Only shows popups for the current authenticated user's bets
- Filters out notifications for other users automatically
- Uses dynamic user ID from authentication store

### 🎨 **Enhanced UX**
- Non-modal overlay design that doesn't block sportsbook interaction
- Smooth entrance/exit animations with scale and translate transforms
- Auto-close functionality with visual countdown timer
- Pause-on-hover feature to prevent accidental closure
- Progress bar with color-coded states (blue = active, yellow = paused)

### ♿ **Accessibility**
- Full keyboard support (ESC key to close)
- Proper ARIA attributes and roles
- Screen reader friendly markup
- Focus management for better navigation

### 🌙 **Theme Compatibility**
- Full dark/light theme support using Tailwind CSS
- Consistent with project design system
- Responsive design for different screen sizes

### 📱 **Responsive Design**
- Optimized for desktop and mobile devices
- Adaptive positioning and sizing
- Touch-friendly interaction areas

## Components

### BetNotification

The main notification component that displays bet confirmation with enhanced print functionality.

```tsx
import { BetNotification } from '@/shared/UI/components';

{isBetPopupOpen && currentBetDetails && (
  <BetNotification
    notification={currentBetDetails}
    onClose={hideBetPopup}
    autoClose={true}
    autoCloseDelay={8000}
  />
)}
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `notification` | `CashierTurboPlaceBetDetails` | - | Bet information to display (required) |
| `onClose` | `() => void` | - | Callback when popup is closed |
| `autoClose` | `boolean` | `false` | Whether to auto-close the popup |
| `autoCloseDelay` | `number` | `5000` | Auto-close delay in ms |
| `className` | `string` | `''` | Additional CSS classes |

#### Key Features

- **Enhanced Print Functionality**: Advanced printer detection and bet slip printing
- **Modal Design**: Full-screen overlay for better focus and user experience
- **Auto-close Support**: Configurable auto-close with timer
- **Responsive Design**: Optimized for both desktop and mobile devices

## Integration

### Sportsbook Store

The popup state is managed in the sportsbook store with the following new properties:

```typescript
interface SportsbookState {
  // ... existing properties
  
  // Bet popup state
  isBetPopupOpen: boolean;
  currentBetDetails: CashierTurboPlaceBetDetails | null;
  currentUserId: number | null;
  
  // Bet popup actions
  showBetPopup: (betDetails: CashierTurboPlaceBetDetails) => void;
  hideBetPopup: () => void;
  setCurrentUserId: (userId: number | null) => void;
}
```

### WebSocket Integration

The WebSocket provider has been enhanced with user context validation:

```typescript
// Only show popup if notification is for current user
if (currentUserIdFromAuth && currentUserIdFromAuth === notificationUserId) {
  showBetPopup(notification);
}
```

### Usage in ContentLayout

The notification is integrated into the content layout as a conditional modal:

```tsx
{isBetPopupOpen && currentBetDetails && (
  <BetNotification
    notification={currentBetDetails}
    onClose={hideBetPopup}
    autoClose={true}
    autoCloseDelay={8000}
  />
)}
```

## Technical Implementation

### State Management
- Uses Zustand store for global state management
- Integrates with existing sportsbook state
- Maintains user context for filtering

### WebSocket Integration
- Leverages existing TurboStars WebSocket service
- Adds user validation layer
- Maintains backward compatibility

### Animation System
- CSS transforms for smooth animations
- Configurable timing and easing
- Performance-optimized transitions

### Auto-close System
- Countdown timer with visual feedback
- Pause-on-hover functionality
- Configurable delay settings

## Migration from Previous System

The new popup system replaces the `BetNotificationOverlay` component in the sportsbook integration while maintaining the existing WebSocket infrastructure. The old notification system is still available for testing purposes but has been removed from production sportsbook integration.

### Breaking Changes
- `BetNotificationOverlay` removed from `InlineSportsbook`
- New popup state properties added to sportsbook store
- User context validation now required for notifications

### Backward Compatibility
- Existing WebSocket service unchanged
- Test pages still functional
- API interfaces remain the same

## Future Enhancements

Potential improvements for future versions:
- Sound notifications for bet placement
- Customizable popup positioning
- Multiple bet notifications queue
- Enhanced bet details with more information
- Integration with user balance updates
- Analytics tracking for bet events
