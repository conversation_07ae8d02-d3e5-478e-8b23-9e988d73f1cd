# Centralized Modal Navigation Hook

## Overview

The `useModalNavigation` hook provides a centralized API for opening modals throughout the application. It replaces scattered modal opening logic with a unified system that maintains URL parameter-based modal control for shareability.

## Features

- **Centralized Modal Opening**: Single source of truth for all modal navigation
- **URL Parameter System**: Maintains shareable URLs with modal state
- **Type Safety**: TypeScript interfaces for all modal operations
- **Convenience Methods**: Pre-configured functions for common operations
- **Consistent Navigation**: Uses Next.js router for proper navigation
- **Backward Compatibility**: Works with existing modal systems

## Usage

### Basic Import

```tsx
import { useModalNavigation } from '@/shared/hooks/ui/useModalNavigation';
```

### User Management Modals

```tsx
const { openCreateUserModal, openEditUserModal, openDeactivateUserModal, openActivateUserModal } = useModalNavigation();

// Open create user modal
openCreateUserModal();

// Open edit user modal
openEditUserModal(123);

// Open deactivate user modal
openDeactivateUserModal(123);

// Open activate user modal
openActivateUserModal(123);
```

### Generic Modal Opening

```tsx
const { handleModalClick, openModal } = useModalNavigation();

// Using generic handleModalClick
handleModalClick({
  modal: 'user-management',
  mode: 'create'
});

// Using openModal utility
openModal('user-management', 'edit', 123);
```

### Wallet Transaction Modals

```tsx
const { openWalletTransactionModal } = useModalNavigation();

// Open wallet modal (global context)
openWalletTransactionModal();

// Open wallet modal with pre-selected user
openWalletTransactionModal(123);
```

### Closing Modals

```tsx
const { closeModal } = useModalNavigation();

// Close any open modal
closeModal();
```

## API Reference

### Interface

```tsx
interface UseModalNavigationReturn {
  // Generic modal opening function
  handleModalClick: (params: ModalParams) => void;
  
  // User management specific functions
  openCreateUserModal: () => void;
  openEditUserModal: (userId: string | number) => void;
  openDeactivateUserModal: (userId: string | number) => void;
  openActivateUserModal: (userId: string | number) => void;
  
  // Wallet transaction specific functions
  openWalletTransactionModal: (userId?: string | number) => void;
  
  // Generic utility for any modal type
  openModal: (modalType: string, mode?: string, entityId?: string | number, additionalParams?: Record<string, string>) => void;
  
  // Close modal function
  closeModal: () => void;
}
```

### Modal Parameters

```tsx
interface ModalParams {
  modal: string;
  mode?: string;
  entityId?: string | number;
  [key: string]: string | number | undefined;
}
```

## URL Parameter System

The hook generates URLs with the following patterns:

- **Create User**: `?modal=user-management&mode=create`
- **Edit User**: `?modal=user-management&mode=edit&userId=123`
- **Deactivate User**: `?modal=user-management&mode=deactivate&userId=123`
- **Activate User**: `?modal=user-management&mode=activate&userId=123`
- **Wallet Transaction**: `?modal=wallet-transaction&entityId=123`

## Integration Examples

### Navigation Components

```tsx
// HorizontalNavigation.tsx
const { handleModalClick } = useModalNavigation();

const handleModalNavigation = (path: string) => {
  if (path.startsWith('?')) {
    const params = new URLSearchParams(path);
    const modal = params.get('modal');
    const mode = params.get('mode');
    const userId = params.get('userId');
    
    if (modal) {
      handleModalClick({
        modal,
        ...(mode && { mode }),
        ...(userId && { entityId: userId })
      });
    }
  }
};
```

### Table Actions

```tsx
// UserTableColumns.tsx
export const getUserTableColumns = (modalNavigation?: ModalNavigationFunctions) => {
  const { openEditUserModal, openDeactivateUserModal, openActivateUserModal } = modalNavigation || {};

  // Use in button onClick handlers
  onClick={() => openEditUserModal(record.id)}
  onClick={() => record.active ? openDeactivateUserModal(record.id) : openActivateUserModal(record.id)}
};
```

### Page Headers

```tsx
// UserManagementPageClient.tsx
const { openCreateUserModal } = useModalNavigation();

<GlobalPageHeader
  title="User Management"
  actions={[
    {
      label: "Add New User",
      icon: "ri-user-add-line",
      onClick: openCreateUserModal,
    }
  ]}
/>
```

## Migration Guide

### Before (Scattered Implementation)

```tsx
// Multiple different implementations
const handleModalClick = (modalPath: string) => {
  const currentUrl = new URL(window.location.href);
  // ... custom URL manipulation
  window.history.pushState({}, '', currentUrl.toString());
};

// Inline modal opening
onClick={() => {
  const currentUrl = new URL(window.location.href);
  currentUrl.searchParams.set('modal', 'user-management');
  currentUrl.searchParams.set('mode', 'edit');
  currentUrl.searchParams.set('userId', userData.id.toString());
  window.history.pushState({}, '', currentUrl.toString());
}}
```

### After (Centralized Implementation)

```tsx
// Single hook import
const { openEditUserModal } = useModalNavigation();

// Simple function call
onClick={() => openEditUserModal(userData.id)}
```

## Benefits

1. **Consistency**: All modal opening logic follows the same pattern
2. **Maintainability**: Single place to update modal navigation logic
3. **Type Safety**: TypeScript interfaces prevent errors
4. **Reusability**: Can be used across any component
5. **URL Shareability**: Maintains existing URL parameter system
6. **Performance**: Optimized with useCallback for re-render prevention

## Related Components

- `GlobalUserManagementModal`: Renders modals based on URL parameters
- `useUserManagementModal`: Business logic for user management operations
- `GlobalPageHeader`: Uses modal navigation for action buttons
- `SpkTable`: Table components use modal navigation for row actions

## Future Extensions

The hook can be easily extended to support new modal types:

```tsx
// Add new modal functions
const openNewModalType = useCallback((entityId?: string | number) => {
  handleModalClick({
    modal: 'new-modal-type',
    entityId
  });
}, [handleModalClick]);
```
