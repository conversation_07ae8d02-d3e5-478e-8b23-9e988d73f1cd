// shared/hooks/useUserManagement.ts - Custom hook for user management business logic
"use client";

import { useUserListQuery } from "@/shared/query";
import { useAuthStore } from "@/shared/stores/authStore";
import { DEFAULT_USER_FILTERS, UserListFilters, UserListResponse } from "@/shared/types/user-management-types";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

interface UseUserManagementReturn {
  // State
  filters: UserListFilters;

  // Data
  userListResponse: any;
  isLoading: boolean;
  isError: boolean;
  error: any;
  isFetching: boolean;

  // Computed values
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;

  // Actions
  handleFilterChange: (newFilters: Partial<UserListFilters>) => void;
  handlePageChange: (page: number) => void;
  handleRefresh: () => void;

  // Authentication state
  isAuthenticated: boolean;
  hasHydrated: boolean;
}

interface UseUserManagementOptions {
  initialUserListResponse?: UserListResponse | null;
  initialFilters?: UserListFilters;
}

/**
 * Custom hook that encapsulates all user management business logic
 * Handles authentication, data fetching, filtering, and pagination
 *
 * Features:
 * - Accepts initial server-side data for SSR optimization
 * - Optimized re-rendering with useCallback for handlers
 * - Graceful fallback to client-side fetching
 */
export const useUserManagement = (options: UseUserManagementOptions = {}): UseUserManagementReturn => {
  const { initialUserListResponse = null, initialFilters } = options;
  const router = useRouter();
  const { isAuthenticated, _hasHydrated } = useAuthStore();

  // State for filters - use initial filters if provided, otherwise use defaults
  const [filters, setFilters] = useState<UserListFilters>(initialFilters || DEFAULT_USER_FILTERS);

  // Fetch user list using the query hook with initial data
  const {
    data: userListResponse,
    isLoading,
    isError,
    error,
    refetch,
    isFetching
  } = useUserListQuery(filters, initialUserListResponse);

  // Redirect if not authenticated
  useEffect(() => {
    if (_hasHydrated && !isAuthenticated) {
      router.replace("/authentication/sign-in/");
    }
  }, [_hasHydrated, isAuthenticated, router]);

  // Handle filter changes
  const handleFilterChange = useCallback((newFilters: Partial<UserListFilters>) => {
    setFilters(prev => {
      const updated = {
        ...prev,
        ...newFilters,
        page: 1 // Reset to first page when filters change
      };
      return updated;
    });
  }, []);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setFilters(prev => ({
      ...prev,
      page
    }));
  }, []);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  // Computed values
  const totalUsers = userListResponse?.count || 0;
  const activeUsers = userListResponse?.data?.filter((user: any) => user.active)?.length || 0;
  const inactiveUsers = totalUsers - activeUsers;

  return {
    // State
    filters,

    // Data
    userListResponse,
    isLoading,
    isError,
    error,
    isFetching,

    // Computed values
    totalUsers,
    activeUsers,
    inactiveUsers,

    // Actions
    handleFilterChange,
    handlePageChange,
    handleRefresh,

    // Authentication state
    isAuthenticated,
    hasHydrated: _hasHydrated
  };
};
