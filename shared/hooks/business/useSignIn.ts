import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useLoginMutation } from "@/shared/query/mutations/useLoginMutation";
import { useAuthStore } from "@/shared/stores/authStore";
import { encodePassword } from "@/shared/utils/passwordEncryption";
import { useAuthenticationBackground } from "@/shared/hooks/ui/useAuthenticationBackground";

export interface UseSignInReturn {
  // Form state
  email: string;
  password: string;
  showPassword: boolean;

  // Validation state
  emailError: string;
  passwordError: string;

  // Authentication state
  isPending: boolean;
  isError: boolean;
  error: Error | null;
  isAuthenticated: boolean;
  twoStepVerificationRequired: boolean;
  hasHydrated: boolean;

  // Handlers
  setEmail: (email: string) => void;
  setPassword: (password: string) => void;
  setShowPassword: (show: boolean) => void;
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void;

  // Background images for UI
  backgroundImages: string[];
}

/**
 * Custom hook for sign-in functionality
 * Handles all authentication logic, form state management, API calls, and side effects
 */
export const useSignIn = (): UseSignInReturn => {
  const router = useRouter();

  // Authentication mutation and store
  const { mutate, isPending, isError, error } = useLoginMutation();
  const { isAuthenticated, twoStepVerificationRequired, _hasHydrated } = useAuthStore();

  // Form state
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);

  // Validation state
  const [emailError, setEmailError] = useState("");
  const [passwordError, setPasswordError] = useState("");

  // Background images for the UI
  const { backgroundImages } = useAuthenticationBackground();

  // Redirection Logic
  useEffect(() => {
    // Only perform redirects AFTER Zustand has rehydrated
    if (_hasHydrated) {
      // If already fully authenticated (e.g., after successful 2SV or direct login without 2SV)
      if (isAuthenticated) {
        router.replace("/dashboard");
      }
      // Temporarily disabled redirect to 2FA page to test inline 2FA form
      // else if (twoStepVerificationRequired) {
      //   router.push("/authentication/two-step-verification/");
      // }
    }
  }, [_hasHydrated, isAuthenticated, twoStepVerificationRequired, router]);

  // Form validation
  const validateForm = (): boolean => {
    setEmailError("");
    setPasswordError("");

    let isValid = true;

    if (!email) {
      setEmailError("Email is required");
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setEmailError("Please enter a valid email address");
      isValid = false;
    }

    if (!password) {
      setPasswordError("Password is required");
      isValid = false;
    }

    return isValid;
  };

  // Form submit handler
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (validateForm()) {
      // Encode the password with base64 before sending to the backend
      const encodedPassword = encodePassword(password);
      mutate({ email, password: encodedPassword, rememberMe: false });
    }
  };

  return {
    // Form state
    email,
    password,
    showPassword,

    // Validation state
    emailError,
    passwordError,

    // Authentication state
    isPending,
    isError,
    error,
    isAuthenticated,
    twoStepVerificationRequired,
    hasHydrated: _hasHydrated,

    // Handlers
    setEmail,
    setPassword,
    setShowPassword,
    handleSubmit,

    // Background images
    backgroundImages,
  };
};
