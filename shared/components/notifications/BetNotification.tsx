// shared/components/notifications/BetNotification.tsx
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { CashierTurboPlaceBetDetails, BetDetailsData } from '@/shared/types/user-management-types';
import { useBetDetailsLazyQuery } from '@/shared/query/useBetDetailsQuery';
import { PrimaryButton } from '@/shared/UI/components';

interface BetNotificationProps {
  notification: CashierTurboPlaceBetDetails;
  onClose?: () => void;
  className?: string;
}

/**
 * Enhanced printer detection interface
 */
interface PrinterInfo {
  id: string;
  name: string;
  type: 'usb' | 'wifi' | 'network' | 'unknown';
  status: 'ready' | 'busy' | 'offline' | 'unknown';
}

/**
 * SimpleBetSuccessPopup Component
 *
 * A simplified success popup that displays when a bet is placed successfully.
 * Replaces the existing BetNotification with enhanced print functionality.
 */
export const BetNotification: React.FC<BetNotificationProps> = ({
  notification,
  onClose,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [_availablePrinters, setAvailablePrinters] = useState<PrinterInfo[]>([]);
  const [isPrintingDetectionLoading, setIsPrintingDetectionLoading] = useState(false);
  const [betDetailsData, setBetDetailsData] = useState<BetDetailsData | null>(null);
  const [isLoadingBetDetails, setIsLoadingBetDetails] = useState(false);

  // Hook for fetching detailed bet information
  const { fetchBetDetails } = useBetDetailsLazyQuery();

  const handleClose = useCallback(() => {
    setIsVisible(false);
    onClose?.();
  }, [onClose]);

  // Handle ESC key press for accessibility
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isVisible) {
        handleClose();
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleEscapeKey);
      // Prevent body scroll when popup is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'unset';
    };
  }, [isVisible, handleClose]);

  // Fetch detailed bet information when popup opens
  useEffect(() => {
    if (isVisible && notification.transactionId && notification.provider && !betDetailsData && !isLoadingBetDetails) {
      setIsLoadingBetDetails(true);
      fetchBetDetails(notification.transactionId, notification.provider)
        .then((response) => {
          setBetDetailsData(response.data);
        })
        .catch((error) => {
          // Handle error silently or show user-friendly message
          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            console.error('Failed to fetch bet details:', error);
          }
        })
        .finally(() => {
          setIsLoadingBetDetails(false);
        });
    }
  }, [isVisible, notification.transactionId, notification.provider, betDetailsData, isLoadingBetDetails, fetchBetDetails]);

  // Enhanced printer detection function
  const detectPrinters = useCallback(async (): Promise<PrinterInfo[]> => {
    setIsPrintingDetectionLoading(true);
    const detectedPrinters: PrinterInfo[] = [];

    try {
      // Modern Web API for printer detection (if available)
      if ('navigator' in window && 'mediaDevices' in navigator) {
        try {
          // Check for USB printers using WebUSB API (if available)
          if ('usb' in navigator) {
            const usbDevices = await (navigator as any).usb.getDevices();
            usbDevices.forEach((device: any, index: number) => {
              // Check if device is a printer (class code 7 for printers)
              if (device.deviceClass === 7 || device.productName?.toLowerCase().includes('printer')) {
                detectedPrinters.push({
                  id: `usb-${device.vendorId}-${device.productId}`,
                  name: device.productName || `USB Printer ${index + 1}`,
                  type: 'usb',
                  status: 'ready'
                });
              }
            });
          }
        } catch (error) {
          // USB printer detection not available - this is expected in most browsers
          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            console.log('USB printer detection not available:', error);
          }
        }
      }

      // Check for network printers using fetch to common printer ports
      const commonPrinterIPs = ['*************', '*************', '**********'];
      const printerCheckPromises = commonPrinterIPs.map(async (ip, index) => {
        try {
          // Try to connect to common printer web interfaces
          const controller = new AbortController();
          setTimeout(() => controller.abort(), 1000); // 1 second timeout

          await fetch(`http://${ip}`, {
            method: 'HEAD',
            signal: controller.signal,
            mode: 'no-cors'
          });

          return {
            id: `network-${ip}`,
            name: `Network Printer ${index + 1} (${ip})`,
            type: 'network' as const,
            status: 'ready' as const
          };
        } catch {
          return null;
        }
      });

      const networkPrinters = (await Promise.all(printerCheckPromises)).filter(Boolean) as PrinterInfo[];
      detectedPrinters.push(...networkPrinters);

      // Fallback: Add default system printer
      if (detectedPrinters.length === 0) {
        detectedPrinters.push({
          id: 'system-default',
          name: 'Default System Printer',
          type: 'unknown',
          status: 'ready'
        });
      }

    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('Printer detection failed:', error);
      }
      // Add fallback printer
      detectedPrinters.push({
        id: 'fallback-printer',
        name: 'System Printer',
        type: 'unknown',
        status: 'unknown'
      });
    } finally {
      setIsPrintingDetectionLoading(false);
    }

    return detectedPrinters;
  }, []);

  // Enhanced print function with comprehensive bet details
  const handlePrintBetslip = useCallback(async () => {
    if (!betDetailsData) {
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.warn('No bet details available for printing');
      }
      return;
    }

    // Detect available printers
    const printers = await detectPrinters();
    setAvailablePrinters(printers);

    // Create comprehensive print content using API response structure
    const printContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Bet Slip - ${betDetailsData.transactionId}</title>
          <style>
            body {
              font-family: 'Rubik', Arial, sans-serif;
              margin: 20px;
              color: #333;
              line-height: 1.6;
              font-size: 12px;
            }
            .header {
              text-align: center;
              border-bottom: 2px solid #E1B649;
              padding-bottom: 20px;
              margin-bottom: 20px;
            }
            .title {
              color: #E1B649;
              font-size: 24px;
              font-weight: bold;
              margin: 0;
            }
            .subtitle {
              color: #666;
              font-size: 16px;
              margin: 5px 0 0 0;
            }
            .section {
              margin: 20px 0;
              padding: 15px;
              border: 1px solid #ddd;
              border-radius: 8px;
            }
            .section-title {
              font-size: 16px;
              font-weight: bold;
              color: #E1B649;
              margin-bottom: 10px;
              border-bottom: 1px solid #E1B649;
              padding-bottom: 5px;
            }
            .detail-row {
              display: flex;
              justify-content: space-between;
              padding: 5px 0;
              border-bottom: 1px solid #eee;
            }
            .detail-row:last-child {
              border-bottom: none;
            }
            .label {
              font-weight: bold;
              color: #333;
              flex: 1;
            }
            .value {
              color: #666;
              flex: 2;
              text-align: right;
            }
            .bet-list {
              margin-top: 15px;
            }
            .bet-item {
              background: #f9f9f9;
              padding: 10px;
              margin: 5px 0;
              border-radius: 5px;
              border-left: 3px solid #E1B649;
            }
            .footer {
              margin-top: 30px;
              text-align: center;
              font-size: 10px;
              color: #999;
              border-top: 1px solid #ddd;
              padding-top: 15px;
            }
            @media print {
              body { margin: 0; }
              .section { break-inside: avoid; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1 class="title">Bet Slip</h1>
            <p class="subtitle">Transaction Confirmation</p>
          </div>

          <!-- Market Details Section -->
          <div class="section">
            <div class="section-title">Market Information</div>
            <div class="detail-row">
              <span class="label">Market ID:</span>
              <span class="value">${betDetailsData.marketId}</span>
            </div>
            <div class="detail-row">
              <span class="label">Market Name:</span>
              <span class="value">${betDetailsData.marketName}</span>
            </div>
            <div class="detail-row">
              <span class="label">Market Status:</span>
              <span class="value">${betDetailsData.marketStatus}</span>
            </div>
          </div>

          <!-- Bet Details Section -->
          <div class="section">
            <div class="section-title">Bet Information</div>
            <div class="detail-row">
              <span class="label">Bet ID:</span>
              <span class="value">${betDetailsData.transactionId}</span>
            </div>
            <div class="detail-row">
              <span class="label">Bet Type:</span>
              <span class="value">${betDetailsData.betType}</span>
            </div>
            <div class="detail-row">
              <span class="label">Settlement Status:</span>
              <span class="value">${betDetailsData.settlementStatus}</span>
            </div>
            <div class="detail-row">
              <span class="label">Bet Amount:</span>
              <span class="value">$${betDetailsData.betAmount.toFixed(2)}</span>
            </div>
            <div class="detail-row">
              <span class="label">Settlement Amount:</span>
              <span class="value">$${(betDetailsData.betAmount || 0).toFixed(2)}</span>
            </div>
            <div class="detail-row">
              <span class="label">Created Date:</span>
              <span class="value">${new Date(betDetailsData.createdDate).toLocaleString()}</span>
            </div>
          </div>

          <!-- Individual Bets Section -->
          ${betDetailsData.betList && betDetailsData.betList.length > 0 ? `
          <div class="section">
            <div class="section-title">Individual Bets</div>
            <div class="bet-list">
              ${betDetailsData.betList.map((bet, index) => `
                <div class="bet-item">
                  <div class="detail-row">
                    <span class="label">Bet ${index + 1} ID:</span>
                    <span class="value">${bet.id}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Market Name:</span>
                    <span class="value">${bet.marketName}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Rate:</span>
                    <span class="value">${bet.rate}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Stake:</span>
                    <span class="value">$${bet.stake.toFixed(2)}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Status:</span>
                    <span class="value">${bet.status}</span>
                  </div>
                  ${bet.selection ? `
                  <div class="detail-row">
                    <span class="label">Selection:</span>
                    <span class="value">${bet.selection}</span>
                  </div>
                  ` : ''}
                </div>
              `).join('')}
            </div>
          </div>
          ` : ''}

          <div class="footer">
            <p>Thank you for your bet. Good luck!</p>
            <p>This is an automatically generated bet slip.</p>
            <p>Printed on: ${new Date().toLocaleString()}</p>
            ${printers.length > 0 ? `<p>Printer: ${printers[0].name}</p>` : ''}
          </div>
        </body>
      </html>
    `;

    // Open print window with enhanced content
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();

      // Wait for content to load before printing
      printWindow.onload = () => {
        printWindow.print();
        printWindow.close();
      };
    } else {
      // Fallback: create temporary element and print
      const printElement = document.createElement('div');
      printElement.innerHTML = printContent;
      printElement.style.display = 'none';
      document.body.appendChild(printElement);
      window.print();
      document.body.removeChild(printElement);
    }
  }, [betDetailsData, detectPrinters]);

  if (!isVisible) return null;

  return (
    <div
      className={`fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50 ${className}`}
      onClick={handleClose}
    >
      {/* Popup Container */}
      <div
        className="relative bg-filter rounded-lg p-5 flex flex-col items-center gap-3"
        style={{
          width: '427px',
          height: '343px',
          backgroundColor: '#1D1D1F',
          borderRadius: '8px',
          padding: '20px',
          gap: '12px'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Icon */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 text-white hover:text-gray-300 transition-colors"
          aria-label="Close popup"
        >
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>

        {/* Success Tick Icon */}
        <div className="flex-shrink-0">
          <img
            src="/assets/svg_icons/bet-slip/tick.svg"
            alt="Success"
            className="w-16 h-16"
          />
        </div>

        {/* Title */}
        <h2
          className="font-rubik font-bold text-center text-primary"
          style={{
            fontSize: '24px',
            lineHeight: '100%',
            textTransform: 'capitalize',
            color: '#E1B649'
          }}
        >
          Bet Placed Successfully
        </h2>

        {/* Description */}
        <p
          className="font-rubik font-normal text-center"
          style={{
            fontSize: '16px',
            lineHeight: '100%',
            color: '#D4D4D4'
          }}
        >
          Your bet has been confirmed and processed. You can print your bet slip below for your records.
        </p>

        {/* Print Betslip Button */}
        <div className="mt-auto">
          <PrimaryButton
            onClick={handlePrintBetslip}
            size="lg"
            className="px-6 py-3"
            disabled={isLoadingBetDetails || isPrintingDetectionLoading}
          >
            {isLoadingBetDetails ? 'Loading...' : isPrintingDetectionLoading ? 'Detecting Printers...' : 'Print Betslip'}
          </PrimaryButton>
        </div>
      </div>
    </div>
  );
};

// Simplified toast component for backward compatibility
interface BetNotificationToastProps {
  notification: CashierTurboPlaceBetDetails;
  onClose?: () => void;
  className?: string;
}

export const BetNotificationToast: React.FC<BetNotificationToastProps> = ({
  notification,
  onClose,
  className = ''
}) => {
  // Use the main BetNotification component for consistency
  return (
    <BetNotification
      notification={notification}
      onClose={onClose}
      className={className}
    />
  );
};
