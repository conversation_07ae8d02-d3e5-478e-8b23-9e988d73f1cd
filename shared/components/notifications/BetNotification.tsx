// shared/components/notifications/BetNotification.tsx
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { CashierTurboPlaceBetDetails, BetDetailsData } from '@/shared/types/user-management-types';
import { useBetDetailsLazyQuery } from '@/shared/query/useBetDetailsQuery';
import { PrimaryButton } from '@/shared/UI/components';

interface BetNotificationProps {
  notification: CashierTurboPlaceBetDetails;
  onClose?: () => void;
  className?: string;
}

/**
 * Enhanced printer detection interface
 */
interface PrinterInfo {
  id: string;
  name: string;
  type: 'usb' | 'wifi' | 'network' | 'unknown';
  status: 'ready' | 'busy' | 'offline' | 'unknown';
}

/**
 * SimpleBetSuccessPopup Component
 *
 * A simplified success popup that displays when a bet is placed successfully.
 * Replaces the existing BetNotification with enhanced print functionality.
 */
export const BetNotification: React.FC<BetNotificationProps> = ({
  notification,
  onClose,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [_availablePrinters, setAvailablePrinters] = useState<PrinterInfo[]>([]);
  const [isPrintingDetectionLoading, setIsPrintingDetectionLoading] = useState(false);
  const [betDetailsData, setBetDetailsData] = useState<BetDetailsData | null>(null);
  const [isLoadingBetDetails, setIsLoadingBetDetails] = useState(false);

  // Hook for fetching detailed bet information
  const { fetchBetDetails } = useBetDetailsLazyQuery();

  const handleClose = useCallback(() => {
    setIsVisible(false);
    onClose?.();
  }, [onClose]);

  // Handle ESC key press for accessibility
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isVisible) {
        handleClose();
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleEscapeKey);
      // Prevent body scroll when popup is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'unset';
    };
  }, [isVisible, handleClose]);

  // Fetch detailed bet information when popup opens
  useEffect(() => {
    if (isVisible && notification.transactionId && notification.provider && !betDetailsData && !isLoadingBetDetails) {
      setIsLoadingBetDetails(true);
      fetchBetDetails(notification.transactionId, notification.provider)
        .then((response) => {
          setBetDetailsData(response.data);
        })
        .catch((error) => {
          // Handle error silently or show user-friendly message
          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            console.error('Failed to fetch bet details:', error);
          }
        })
        .finally(() => {
          setIsLoadingBetDetails(false);
        });
    }
  }, [isVisible, notification.transactionId, notification.provider, betDetailsData, isLoadingBetDetails, fetchBetDetails]);

  // Enhanced printer detection function
  const detectPrinters = useCallback(async (): Promise<PrinterInfo[]> => {
    setIsPrintingDetectionLoading(true);
    const detectedPrinters: PrinterInfo[] = [];

    try {
      // Modern Web API for printer detection (if available)
      if ('navigator' in window && 'mediaDevices' in navigator) {
        try {
          // Check for USB printers using WebUSB API (if available)
          if ('usb' in navigator) {
            const usbDevices = await (navigator as any).usb.getDevices();
            usbDevices.forEach((device: any, index: number) => {
              // Check if device is a printer (class code 7 for printers)
              if (device.deviceClass === 7 || device.productName?.toLowerCase().includes('printer')) {
                detectedPrinters.push({
                  id: `usb-${device.vendorId}-${device.productId}`,
                  name: device.productName || `USB Printer ${index + 1}`,
                  type: 'usb',
                  status: 'ready'
                });
              }
            });
          }
        } catch (error) {
          // USB printer detection not available - this is expected in most browsers
          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            console.log('USB printer detection not available:', error);
          }
        }
      }

      // Check for network printers using fetch to common printer ports
      const commonPrinterIPs = ['*************', '*************', '**********'];
      const printerCheckPromises = commonPrinterIPs.map(async (ip, index) => {
        try {
          // Try to connect to common printer web interfaces
          const controller = new AbortController();
          setTimeout(() => controller.abort(), 1000); // 1 second timeout

          await fetch(`http://${ip}`, {
            method: 'HEAD',
            signal: controller.signal,
            mode: 'no-cors'
          });

          return {
            id: `network-${ip}`,
            name: `Network Printer ${index + 1} (${ip})`,
            type: 'network' as const,
            status: 'ready' as const
          };
        } catch {
          return null;
        }
      });

      const networkPrinters = (await Promise.all(printerCheckPromises)).filter(Boolean) as PrinterInfo[];
      detectedPrinters.push(...networkPrinters);

      // Fallback: Add default system printer
      if (detectedPrinters.length === 0) {
        detectedPrinters.push({
          id: 'system-default',
          name: 'Default System Printer',
          type: 'unknown',
          status: 'ready'
        });
      }

    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('Printer detection failed:', error);
      }
      // Add fallback printer
      detectedPrinters.push({
        id: 'fallback-printer',
        name: 'System Printer',
        type: 'unknown',
        status: 'unknown'
      });
    } finally {
      setIsPrintingDetectionLoading(false);
    }

    return detectedPrinters;
  }, []);

  // Enhanced print function with comprehensive bet details
  const handlePrintBetslip = useCallback(async () => {
    if (!betDetailsData) {
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.warn('No bet details available for printing');
      }
      return;
    }

    // Detect available printers
    const printers = await detectPrinters();
    setAvailablePrinters(printers);

    // Create comprehensive print content using API response structure
    const printContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Bet Slip - ${betDetailsData.betDetails?.betId || 'N/A'}</title>
          <style>
            body {
              font-family: 'Rubik', Arial, sans-serif;
              margin: 20px;
              color: #333;
              line-height: 1.6;
              font-size: 12px;
            }
            .header {
              text-align: center;
              border-bottom: 2px solid #E1B649;
              padding-bottom: 20px;
              margin-bottom: 20px;
            }
            .title {
              color: #E1B649;
              font-size: 24px;
              font-weight: bold;
              margin: 0;
            }
            .subtitle {
              color: #666;
              font-size: 16px;
              margin: 5px 0 0 0;
            }
            .section {
              margin: 20px 0;
              padding: 15px;
              border: 1px solid #ddd;
              border-radius: 8px;
            }
            .section-title {
              font-size: 16px;
              font-weight: bold;
              color: #E1B649;
              margin-bottom: 10px;
              border-bottom: 1px solid #E1B649;
              padding-bottom: 5px;
            }
            .detail-row {
              display: flex;
              justify-content: space-between;
              padding: 5px 0;
              border-bottom: 1px solid #eee;
            }
            .detail-row:last-child {
              border-bottom: none;
            }
            .label {
              font-weight: bold;
              color: #333;
              flex: 1;
            }
            .value {
              color: #666;
              flex: 2;
              text-align: right;
            }
            .bet-list {
              margin-top: 15px;
            }
            .bet-item {
              background: #f9f9f9;
              padding: 10px;
              margin: 5px 0;
              border-radius: 5px;
              border-left: 3px solid #E1B649;
            }
            .footer {
              margin-top: 30px;
              text-align: center;
              font-size: 10px;
              color: #999;
              border-top: 1px solid #ddd;
              padding-top: 15px;
            }
            @media print {
              body { margin: 0; }
              .section { break-inside: avoid; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1 class="title">Bet Slip</h1>
            <p class="subtitle">Transaction Confirmation</p>
          </div>

          <!-- Market Details Section -->
          <div class="section">
            <div class="section-title">Market Information</div>
            <div class="detail-row">
              <span class="label">Market ID:</span>
              <span class="value">${betDetailsData.marketDetail?.marketId || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Market Name:</span>
              <span class="value">${betDetailsData.marketDetail?.marketName || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Market Status:</span>
              <span class="value">${betDetailsData.marketDetail?.marketStatus || 'N/A'}</span>
            </div>
          </div>

          <!-- Bet Details Section -->
          <div class="section">
            <div class="section-title">Bet Information</div>
            <div class="detail-row">
              <span class="label">Bet ID:</span>
              <span class="value">${betDetailsData.betDetails?.betId || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Bet Type:</span>
              <span class="value">${betDetailsData.betDetails?.betType || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Settlement Status:</span>
              <span class="value">${betDetailsData.betDetails?.settlementStatus || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="label">Bet Amount:</span>
              <span class="value">$${(betDetailsData.betDetails?.betAmount || 0).toFixed(2)}</span>
            </div>
            <div class="detail-row">
              <span class="label">Settlement Amount:</span>
              <span class="value">$${(betDetailsData.betDetails?.settlementAmount || 0).toFixed(2)}</span>
            </div>
            <div class="detail-row">
              <span class="label">Created Date:</span>
              <span class="value">${betDetailsData.betDetails?.createdDate ? new Date(betDetailsData.betDetails.createdDate).toLocaleString() : 'N/A'}</span>
            </div>
          </div>

          <!-- Individual Bets Section -->
          ${betDetailsData.betList && betDetailsData.betList.length > 0 ? `
          <div class="section">
            <div class="section-title">Individual Bets</div>
            <div class="bet-list">
              ${betDetailsData.betList.map((bet, index) => `
                <div class="bet-item">
                  <div class="detail-row">
                    <span class="label">Bet ${index + 1} ID:</span>
                    <span class="value">${bet.betId || 'N/A'}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Market Name:</span>
                    <span class="value">${bet.marketName || 'N/A'}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Bet Type:</span>
                    <span class="value">${bet.betType || 'N/A'}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Rate:</span>
                    <span class="value">${bet.rate || 'N/A'}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Stake:</span>
                    <span class="value">$${(bet.stake || 0).toFixed(2)}</span>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
          ` : ''}

          <div class="footer">
            <p>Thank you for your bet. Good luck!</p>
            <p>This is an automatically generated bet slip.</p>
            <p>Printed on: ${new Date().toLocaleString()}</p>
            ${printers.length > 0 ? `<p>Printer: ${printers[0].name}</p>` : ''}
          </div>
        </body>
      </html>
    `;

    // Open print window with enhanced content
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();

      // Wait for content to load before printing
      printWindow.onload = () => {
        printWindow.print();
        printWindow.close();
      };
    } else {
      // Fallback: create temporary element and print
      const printElement = document.createElement('div');
      printElement.innerHTML = printContent;
      printElement.style.display = 'none';
      document.body.appendChild(printElement);
      window.print();
      document.body.removeChild(printElement);
    }
  }, [betDetailsData, detectPrinters]);

  if (!isVisible) return null;

  return (
    <div
      className={`fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50 ${className}`}
      onClick={handleClose}
    >
      {/* Popup Container */}
      <div
        className="relative bg-filter rounded-lg p-5 flex flex-col items-center gap-3"
        style={{
          width: '427px',
          height: '343px',
          backgroundColor: '#1D1D1F',
          borderRadius: '8px',
          padding: '20px',
          gap: '12px'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Icon */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 text-white hover:text-gray-300 transition-colors"
          aria-label="Close popup"
        >
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>

        {/* Success Tick Icon */}
        <div className="flex-shrink-0">
          <img
            src="/assets/svg_icons/bet-slip/tick.svg"
            alt="Success"
            className="<svg width="800" height="800" viewBox="0 0 800 800" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M400 8.03174C616.474 8.03174 791.968 183.526 791.968 400C791.968 616.474 616.474 791.969 400 791.969C183.526 791.969 8.03125 616.474 8.03125 400C8.03125 183.526 183.526 8.03174 400 8.03174ZM683.627 380.394H752.207C747.681 297.754 714.705 222.682 662.915 164.817L614.414 213.328C653.91 258.654 679.281 316.642 683.627 380.394ZM752.207 419.607H683.627C679.281 483.368 653.92 541.346 614.414 586.682L662.915 635.183C714.705 577.318 747.681 502.256 752.207 419.607ZM635.183 662.916L586.681 614.414C541.346 653.92 483.367 679.272 419.606 683.618V752.208C502.256 747.682 577.318 714.714 635.183 662.916ZM380.393 752.208V683.628C316.642 679.281 258.654 653.911 213.327 614.414L164.817 662.916C222.682 714.705 297.753 747.682 380.393 752.208ZM137.084 635.193L185.595 586.682C146.089 541.346 120.728 483.368 116.382 419.607H47.7922C52.3182 502.256 85.2948 577.328 137.084 635.193ZM154.933 400C152.571 534.382 267.328 645.076 400 645.076C535.355 645.076 645.076 535.356 645.076 400C645.076 264.692 535.412 154.99 400.104 154.924C267.375 154.924 152.571 265.533 154.933 400ZM47.7922 380.394H116.382C120.719 316.642 146.089 258.654 185.595 213.318L137.094 164.817C85.2948 222.682 52.3182 297.754 47.7922 380.394ZM380.393 116.382V47.7927C297.753 52.3187 222.682 85.2953 164.817 137.094L213.318 185.595C258.654 146.09 316.642 120.719 380.393 116.382ZM419.606 47.7927V116.382C483.367 120.719 541.346 146.09 586.681 185.595L635.183 137.094C577.318 85.2953 502.256 52.3187 419.606 47.7927Z" fill="white"/>
<path d="M454.277 450.96H523.901C522.944 477.119 517.681 499.21 508.11 517.234C498.54 535.099 484.902 548.657 467.197 557.908C449.652 567.16 428.438 571.785 403.555 571.785C384.255 571.785 367.108 568.595 352.114 562.215C337.121 555.675 324.36 546.105 313.833 533.504C303.306 520.903 295.33 505.351 289.907 486.849C284.644 468.187 282.012 446.733 282.012 422.488V363.392C282.012 339.147 284.803 317.693 290.386 299.031C296.128 280.369 304.263 264.738 314.79 252.137C325.477 239.376 338.317 229.806 353.311 223.426C368.464 216.886 385.531 213.616 404.512 213.616C429.873 213.616 451.087 218.401 468.154 227.972C485.221 237.542 498.381 251.499 507.632 269.842C517.043 288.025 522.705 310.276 524.619 336.595H454.756C454.277 320.006 452.363 307.007 449.014 297.596C445.824 288.025 440.719 281.326 433.701 277.498C426.683 273.51 416.953 271.517 404.512 271.517C395.26 271.517 387.285 273.191 380.586 276.541C373.887 279.731 368.384 284.915 364.077 292.093C359.771 299.271 356.58 308.681 354.507 320.325C352.593 331.969 351.636 346.165 351.636 362.913V422.488C351.636 438.917 352.513 452.954 354.268 464.598C356.022 476.082 358.813 485.493 362.642 492.83C366.47 500.167 371.654 505.511 378.193 508.86C384.893 512.21 393.346 513.885 403.555 513.885C415.358 513.885 424.849 512.13 432.026 508.621C439.364 505.112 444.787 498.812 448.296 489.72C451.805 480.628 453.799 467.708 454.277 450.96Z" fill="white"/>
</svg>
"
          />
        </div>

        {/* Title */}
        <h2
          className="font-rubik font-bold text-center text-primary"
          style={{
            fontSize: '24px',
            lineHeight: '100%',
            textTransform: 'capitalize',
            color: '#E1B649'
          }}
        >
          Bet Placed Successfully
        </h2>

        {/* Description */}
        <p
          className="font-rubik font-normal text-center"
          style={{
            fontSize: '16px',
            lineHeight: '100%',
            color: '#D4D4D4'
          }}
        >
          Your bet has been confirmed and processed. You can print your bet slip below for your records.
        </p>

        {/* Print Betslip Button */}
        <div className="mt-auto">
          <PrimaryButton
            onClick={handlePrintBetslip}
            size="lg"
            className="px-6 py-3"
            disabled={isLoadingBetDetails || isPrintingDetectionLoading}
          >
            {isLoadingBetDetails ? 'Loading...' : isPrintingDetectionLoading ? 'Detecting Printers...' : 'Print Betslip'}
          </PrimaryButton>
        </div>
      </div>
    </div>
  );
};

// Simplified toast component for backward compatibility
interface BetNotificationToastProps {
  notification: CashierTurboPlaceBetDetails;
  onClose?: () => void;
  className?: string;
}

export const BetNotificationToast: React.FC<BetNotificationToastProps> = ({
  notification,
  onClose,
  className = ''
}) => {
  // Use the main BetNotification component for consistency
  return (
    <BetNotification
      notification={notification}
      onClose={onClose}
      className={className}
    />
  );
};
