// middleware.ts - Performance, security, and authentication middleware for Next.js
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

/**
 * Check if the request has authentication cookies
 */
function hasAuthCookies(request: NextRequest): boolean {
  const authToken = request.cookies.get('auth-token');
  const authUser = request.cookies.get('auth-user');
  return !!(authToken?.value && authUser?.value);
}

/**
 * Middleware for performance optimization, security headers, and authentication
 * Runs on all requests to add caching, compression, security headers, and auth context
 */
export function middleware(request: NextRequest) {
  const response = NextResponse.next();
  const { pathname } = request.nextUrl;

  // Add authentication context to response headers for server components
  const isAuthenticated = hasAuthCookies(request);
  response.headers.set('x-auth-status', isAuthenticated ? 'authenticated' : 'unauthenticated');

  // Add security headers for all requests
  response.headers.set('X-DNS-Prefetch-Control', 'on');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('X-Frame-Options', 'SAMEORIGIN');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

  // Add performance headers
  response.headers.set('X-Powered-By', 'Next.js');
  
  // Cache control for different types of content
  if (pathname.startsWith('/_next/static/')) {
    // Static assets - cache for 1 year
    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
  } else if (pathname.startsWith('/api/')) {
    // API routes - cache for 5 minutes with stale-while-revalidate
    response.headers.set('Cache-Control', 'public, max-age=300, stale-while-revalidate=60');
  } else if (pathname.match(/\.(jpg|jpeg|png|gif|webp|avif|ico|svg)$/)) {
    // Images - cache for 1 day
    response.headers.set('Cache-Control', 'public, max-age=86400, stale-while-revalidate=3600');
  } else if (pathname.match(/\.(css|js|woff|woff2|ttf|eot)$/)) {
    // CSS, JS, and fonts - cache for 1 week
    response.headers.set('Cache-Control', 'public, max-age=604800, stale-while-revalidate=86400');
  } else {
    // HTML pages - cache for 1 hour with stale-while-revalidate
    response.headers.set('Cache-Control', 'public, max-age=3600, stale-while-revalidate=1800');
  }

  // Add compression hint
  response.headers.set('Vary', 'Accept-Encoding');

  // Add preload hints for critical resources on main pages
  if (pathname === '/' || pathname === '/dashboard' || pathname === '/user-management') {
    response.headers.set('Link', [
      '</assets/css/styles.css>; rel=preload; as=style',
      '</assets/fonts/inter.woff2>; rel=preload; as=font; type=font/woff2; crossorigin',
      '</assets/images/logo.svg>; rel=preload; as=image'
    ].join(', '));
  }

  // Add early hints for DNS prefetch
  response.headers.set('Link', [
    '<https://fonts.googleapis.com>; rel=dns-prefetch',
    '<https://fonts.gstatic.com>; rel=dns-prefetch',
    '<https://staging-reports-api.8dexsuperadmin.com>; rel=dns-prefetch'
  ].join(', '));

  return response;
}

/**
 * Configure which paths the middleware should run on
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
